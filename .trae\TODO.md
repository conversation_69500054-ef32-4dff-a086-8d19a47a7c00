# TODO:

- [x] fix-tab-visibility-width-based: Implement width-based tab visibility logic using window.innerWidth instead of Material-UI breakpoints (priority: High)
- [x] add-resize-listener: Add window resize event listener to dynamically update tab visibility when window size changes (priority: High)
- [x] test-100-zoom-visibility: Test that all 7 tabs are visible at 100% zoom on desktop screens (1200px+) (priority: Medium)
